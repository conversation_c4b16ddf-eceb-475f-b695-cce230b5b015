# 基础知识

## 目录介绍

├── feature # 插件市场插件
│   ├── README.md
│   ├── babel.config.js
│   ├── package-lock.json
│   ├── package.json
│   ├── public
│   ├── src
│   ├── tsconfig.json
│   └── vue.config.js
├── public # rubick __static 目录
│   ├── favicon.ico
│   ├── feature
│   ├── icons
│   ├── index.html
│   ├── preload.js
│   └── tpl
├── src # rubick 核心源码
│   ├── common # 一些通用的函数
│   ├── core # 一些核心的能力，比如 app search
│   ├── main # 主进程
│   └── renderer # 渲染进程
├── tpl # rubick 模板插件
│   ├── README.md
│   ├── babel.config.js
│   ├── package-lock.json
│   ├── package.json
│   ├── public
│   ├── src
│   ├── tsconfig.json
│   └── vue.config.js
├── LICENSE # MIT 协议
├── README.md # 英文文档
├── README.zh-CN.md # 中文文档
├── babel.config.js
├── deploy-doc.sh # rubick doc 发布脚本
├── tsconfig.json
├── package-lock.json
├── package.json
└── vue.config.js



# 启动

rubick 启动主要涉及到3个目录：

1. 根目录：rubick 核心进程
2. feature：rubick 内置的插件市场插件
3. tpl: rubick 内置的模板插件

```
$ npm i
$ cd feature && npm i
$ cd tpl && npm i
```


```
2. 启动核心进程
$ npm run electron:serve
3. 启动插件中心 非必须
$ cd feature && npm run serve
4. 启动模板插件 非必须
$ cd tpl && npm run serve
```

## 编译

```
$ cd feature && npm run build
$ cd tpl && npm run build
$ npm run electron:build
```
