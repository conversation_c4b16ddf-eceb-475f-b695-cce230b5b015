(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1af68a6e"],{5458:function(e,t,n){"use strict";n.r(t);var c=n("c7eb"),r=n("1da1"),a=(n("d81d"),n("d3b7"),n("159b"),n("b0c0"),n("7a23")),o=n("0eaf"),u=n("be89"),i=n("5502"),s={class:"worker"},b={__name:"worker",setup:function(e){var t=Object(i["b"])(),n=Object(a["computed"])((function(){return t.state.totalPlugins})),b=Object(a["ref"])([]);Object(a["onBeforeMount"])(Object(r["a"])(Object(c["a"])().mark((function e(){return Object(c["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,o["a"].getWorkerDetail();case 2:b.value=e.sent;case 3:case"end":return e.stop()}}),e)}))));var f=Object(a["computed"])((function(){var e=b.value||[];return e.length?e.map((function(e){var t=null;return n.value.forEach((function(n){n.name===e&&(t=n)})),t})):[]}));return function(e,t){return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",s,[Object(a["unref"])(f)&&Object(a["unref"])(f).length?(Object(a["openBlock"])(),Object(a["createBlock"])(u["a"],{key:0,onDownloadSuccess:e.downloadSuccess,title:e.$t("feature.market.efficiency"),list:Object(a["unref"])(f)},null,8,["onDownloadSuccess","title","list"])):Object(a["createCommentVNode"])("",!0)])}}};n("b63b");const f=b;t["default"]=f},"6a1f":function(e,t,n){},b63b:function(e,t,n){"use strict";n("6a1f")},d81d:function(e,t,n){"use strict";var c=n("23e7"),r=n("b727").map,a=n("1dde"),o=a("map");c({target:"Array",proto:!0,forced:!o},{map:function(e){return r(this,e,arguments.length>1?arguments[1]:void 0)}})}}]);