(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a2b32e48"],{4001:function(e,t,n){},"4ebf":function(e,t,n){"use strict";n.r(t);var r=n("c7eb"),o=n("1da1"),a=(n("d81d"),n("d3b7"),n("159b"),n("b0c0"),n("9911"),n("7a23"));n("4001");
/**
 * Vue 3 Carousel 0.3.1
 * (c) 2023
 * @license MIT
 */
const i={itemsToShow:1,itemsToScroll:1,modelValue:0,transition:300,autoplay:0,snapAlign:"center",wrapAround:!1,throttle:16,pauseAutoplayOnHover:!1,mouseDrag:!0,touchDrag:!0,dir:"ltr",breakpoints:void 0,i18n:{ariaNextSlide:"Navigate to next slide",ariaPreviousSlide:"Navigate to previous slide",ariaNavigateToSlide:"Navigate to slide {slideNumber}",ariaGallery:"Gallery",itemXofY:"Item {currentSlide} of {slidesCount}",iconArrowUp:"Arrow pointing upwards",iconArrowDown:"Arrow pointing downwards",iconArrowRight:"Arrow pointing to the right",iconArrowLeft:"Arrow pointing to the left"}},c={itemsToShow:{default:i.itemsToShow,type:Number},itemsToScroll:{default:i.itemsToScroll,type:Number},wrapAround:{default:i.wrapAround,type:Boolean},throttle:{default:i.throttle,type:Number},snapAlign:{default:i.snapAlign,validator(e){return["start","end","center","center-even","center-odd"].includes(e)}},transition:{default:i.transition,type:Number},breakpoints:{default:i.breakpoints,type:Object},autoplay:{default:i.autoplay,type:Number},pauseAutoplayOnHover:{default:i.pauseAutoplayOnHover,type:Boolean},modelValue:{default:void 0,type:Number},mouseDrag:{default:i.mouseDrag,type:Boolean},touchDrag:{default:i.touchDrag,type:Boolean},dir:{default:i.dir,validator(e){return["rtl","ltr"].includes(e)}},i18n:{default:i.i18n,type:Object},settings:{default(){return{}},type:Object}};function l({config:e,slidesCount:t}){const{snapAlign:n,wrapAround:r,itemsToShow:o=1}=e;if(r)return Math.max(t-1,0);let a;switch(n){case"start":a=t-o;break;case"end":a=t-1;break;case"center":case"center-odd":a=t-Math.ceil((o-.5)/2);break;case"center-even":a=t-Math.ceil(o/2);break;default:a=0;break}return Math.max(a,0)}function u({config:e,slidesCount:t}){const{wrapAround:n,snapAlign:r,itemsToShow:o=1}=e;let a=0;if(n||o>t)return a;switch(r){case"start":a=0;break;case"end":a=o-1;break;case"center":case"center-odd":a=Math.floor((o-1)/2);break;case"center-even":a=Math.floor((o-2)/2);break;default:a=0;break}return a}function s({val:e,max:t,min:n}){return t<n?e:Math.min(Math.max(e,n),t)}function d({config:e,currentSlide:t,slidesCount:n}){const{snapAlign:r,wrapAround:o,itemsToShow:a=1}=e;let i=t;switch(r){case"center":case"center-odd":i-=(a-1)/2;break;case"center-even":i-=(a-2)/2;break;case"end":i-=a-1;break}return o?i:s({val:i,max:n-a,min:0})}function v(e){return e?e.reduce((e,t)=>{var n;return t.type===a["Fragment"]?[...e,...v(t.children)]:"CarouselSlide"===(null===(n=t.type)||void 0===n?void 0:n.name)?[...e,t]:e},[]):[]}function b({val:e,max:t,min:n=0}){return e>t?b({val:e-(t+1),max:t,min:n}):e<n?b({val:e+(t+1),max:t,min:n}):e}function f(e,t){let n;return t?function(...r){const o=this;n||(e.apply(o,r),n=!0,setTimeout(()=>n=!1,t))}:e}function p(e,t){let n;return function(...r){n&&clearTimeout(n),n=setTimeout(()=>{e(...r),n=null},t)}}function m(e="",t={}){return Object.entries(t).reduce((e,[t,n])=>e.replace(`{${t}}`,String(n)),e)}var j,O=Object(a["defineComponent"])({name:"ARIA",setup(){const e=Object(a["inject"])("config",Object(a["reactive"])(Object.assign({},i))),t=Object(a["inject"])("currentSlide",Object(a["ref"])(0)),n=Object(a["inject"])("slidesCount",Object(a["ref"])(0));return()=>Object(a["h"])("div",{class:["carousel__liveregion","carousel__sr-only"],"aria-live":"polite","aria-atomic":"true"},m(e.i18n["itemXofY"],{currentSlide:t.value+1,slidesCount:n.value}))}}),h=Object(a["defineComponent"])({name:"Carousel",props:c,setup(e,{slots:t,emit:n,expose:r}){var o;const m=Object(a["ref"])(null),j=Object(a["ref"])([]),h=Object(a["ref"])(0),g=Object(a["ref"])(0),w=Object(a["reactive"])(Object.assign({},i));let x,k=Object.assign({},i);const S=Object(a["ref"])(null!==(o=e.modelValue)&&void 0!==o?o:0),y=Object(a["ref"])(0),C=Object(a["ref"])(0),A=Object(a["ref"])(0),T=Object(a["ref"])(0);let _,N;function M(){x=Object.assign({},e.breakpoints),k=Object.assign(Object.assign(Object.assign({},k),e),{i18n:Object.assign(Object.assign({},k.i18n),e.i18n),breakpoints:void 0}),L(k)}function E(){if(!x||!Object.keys(x).length)return;const e=Object.keys(x).map(e=>Number(e)).sort((e,t)=>+t-+e);let t=Object.assign({},k);e.some(e=>{const n=window.matchMedia(`(min-width: ${e}px)`).matches;return n&&(t=Object.assign(Object.assign({},t),x[e])),n}),L(t)}function L(e){Object.entries(e).forEach(([e,t])=>w[e]=t)}Object(a["provide"])("config",w),Object(a["provide"])("slidesCount",g),Object(a["provide"])("currentSlide",S),Object(a["provide"])("maxSlide",A),Object(a["provide"])("minSlide",T),Object(a["provide"])("slideWidth",h);const B=p(()=>{E(),D()},16);function D(){if(!m.value)return;const e=m.value.getBoundingClientRect();h.value=e.width/w.itemsToShow}function V(){g.value<=0||(C.value=Math.ceil((g.value-1)/2),A.value=l({config:w,slidesCount:g.value}),T.value=u({config:w,slidesCount:g.value}),w.wrapAround||(S.value=s({val:S.value,max:A.value,min:T.value})))}Object(a["onMounted"])(()=>{Object(a["nextTick"])(()=>D()),setTimeout(()=>D(),1e3),E(),W(),window.addEventListener("resize",B,{passive:!0}),n("init")}),Object(a["onUnmounted"])(()=>{N&&clearTimeout(N),_&&clearInterval(_),window.removeEventListener("resize",B,{passive:!0})});let I=!1;const U={x:0,y:0},X={x:0,y:0},R=Object(a["reactive"])({x:0,y:0}),$=Object(a["ref"])(!1),z=Object(a["ref"])(!1),Y=()=>{$.value=!0},P=()=>{$.value=!1};function H(e){["INPUT","TEXTAREA","SELECT"].includes(e.target.tagName)||(I="touchstart"===e.type,I||e.preventDefault(),!I&&0!==e.button||q.value||(U.x=I?e.touches[0].clientX:e.clientX,U.y=I?e.touches[0].clientY:e.clientY,document.addEventListener(I?"touchmove":"mousemove",F,!0),document.addEventListener(I?"touchend":"mouseup",G,!0)))}const F=f(e=>{z.value=!0,X.x=I?e.touches[0].clientX:e.clientX,X.y=I?e.touches[0].clientY:e.clientY;const t=X.x-U.x,n=X.y-U.y;R.y=n,R.x=t},w.throttle);function G(){const e="rtl"===w.dir?-1:1,t=.4*Math.sign(R.x),n=Math.round(R.x/h.value+t)*e;if(n&&!I){const e=t=>{t.stopPropagation(),window.removeEventListener("click",e,!0)};window.addEventListener("click",e,!0)}K(S.value-n),R.x=0,R.y=0,z.value=!1,document.removeEventListener(I?"touchmove":"mousemove",F,!0),document.removeEventListener(I?"touchend":"mouseup",G,!0)}function W(){!w.autoplay||w.autoplay<=0||(_=setInterval(()=>{w.pauseAutoplayOnHover&&$.value||Q()},w.autoplay))}function J(){_&&(clearInterval(_),_=null),W()}const q=Object(a["ref"])(!1);function K(e){const t=w.wrapAround?e:s({val:e,max:A.value,min:T.value});S.value===t||q.value||(n("slide-start",{slidingToIndex:e,currentSlideIndex:S.value,prevSlideIndex:y.value,slidesCount:g.value}),q.value=!0,y.value=S.value,S.value=t,N=setTimeout(()=>{if(w.wrapAround){const r=b({val:t,max:A.value,min:0});r!==S.value&&(S.value=r,n("loop",{currentSlideIndex:S.value,slidingToIndex:e}))}n("update:modelValue",S.value),n("slide-end",{currentSlideIndex:S.value,prevSlideIndex:y.value,slidesCount:g.value}),q.value=!1,J()},w.transition))}function Q(){K(S.value+w.itemsToScroll)}function Z(){K(S.value-w.itemsToScroll)}const ee={slideTo:K,next:Q,prev:Z};Object(a["provide"])("nav",ee),Object(a["provide"])("isSliding",q);const te=Object(a["computed"])(()=>d({config:w,currentSlide:S.value,slidesCount:g.value}));Object(a["provide"])("slidesToScroll",te);const ne=Object(a["computed"])(()=>{const e="rtl"===w.dir?-1:1,t=te.value*h.value*e;return{transform:`translateX(${R.x-t}px)`,transition:(q.value?w.transition:0)+"ms",margin:w.wrapAround?`0 -${g.value*h.value}px`:"",width:"100%"}});function re(){M(),E(),V(),D(),J()}Object.keys(c).forEach(t=>{["modelValue"].includes(t)||Object(a["watch"])(()=>e[t],re)}),Object(a["watch"])(()=>e["modelValue"],e=>{e!==S.value&&K(Number(e))}),Object(a["watch"])(g,V),n("before-init"),M();const oe={config:w,slidesCount:g,slideWidth:h,next:Q,prev:Z,slideTo:K,currentSlide:S,maxSlide:A,minSlide:T,middleSlide:C};r({updateBreakpointsConfigs:E,updateSlidesData:V,updateSlideWidth:D,initDefaultConfigs:M,restartCarousel:re,slideTo:K,next:Q,prev:Z,nav:ee,data:oe});const ae=t.default||t.slides,ie=t.addons,ce=Object(a["reactive"])(oe);return()=>{const e=v(null===ae||void 0===ae?void 0:ae(ce)),t=(null===ie||void 0===ie?void 0:ie(ce))||[];e.forEach((e,t)=>e.props.index=t);let n=e;if(w.wrapAround){const t=e.map((t,n)=>Object(a["cloneVNode"])(t,{index:-e.length+n,isClone:!0,key:"clone-before-"+n})),r=e.map((t,n)=>Object(a["cloneVNode"])(t,{index:e.length+n,isClone:!0,key:"clone-after-"+n}));n=[...t,...e,...r]}j.value=e,g.value=Math.max(e.length,1);const r=Object(a["h"])("ol",{class:"carousel__track",style:ne.value,onMousedownCapture:w.mouseDrag?H:null,onTouchstartPassiveCapture:w.touchDrag?H:null},n),o=Object(a["h"])("div",{class:"carousel__viewport"},r);return Object(a["h"])("section",{ref:m,class:{carousel:!0,"is-sliding":q.value,"is-dragging":z.value,"is-hover":$.value,"carousel--rtl":"rtl"===w.dir},dir:w.dir,"aria-label":w.i18n["ariaGallery"],tabindex:"0",onMouseenter:Y,onMouseleave:P},[o,t,Object(a["h"])(O)])}}});(function(e){e["arrowUp"]="arrowUp",e["arrowDown"]="arrowDown",e["arrowRight"]="arrowRight",e["arrowLeft"]="arrowLeft"})(j||(j={}));const g={arrowUp:"M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z",arrowDown:"M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z",arrowRight:"M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z",arrowLeft:"M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"};function w(e){return e in j}const x=e=>{const t=Object(a["inject"])("config",Object(a["reactive"])(Object.assign({},i))),n=String(e.name),r="icon"+(n.charAt(0).toUpperCase()+n.slice(1));if(!n||"string"!==typeof n||!w(n))return;const o=g[n],c=Object(a["h"])("path",{d:o}),l=t.i18n[r]||e.title||n,u=Object(a["h"])("title",l);return Object(a["h"])("svg",{class:"carousel__icon",viewBox:"0 0 24 24",role:"img","aria-label":l},[u,c])};x.props={name:String,title:String};var k=Object(a["defineComponent"])({name:"CarouselSlide",props:{index:{type:Number,default:1},isClone:{type:Boolean,default:!1}},setup(e,{slots:t}){const n=Object(a["inject"])("config",Object(a["reactive"])(Object.assign({},i))),r=Object(a["inject"])("currentSlide",Object(a["ref"])(0)),o=Object(a["inject"])("slidesToScroll",Object(a["ref"])(0)),c=Object(a["inject"])("isSliding",Object(a["ref"])(!1)),l=()=>e.index===r.value,u=()=>e.index===r.value-1,s=()=>e.index===r.value+1,d=()=>{const t=Math.floor(o.value),r=Math.ceil(o.value+n.itemsToShow-1);return e.index>=t&&e.index<=r};return()=>{var r;return Object(a["h"])("li",{style:{width:100/n.itemsToShow+"%"},class:{carousel__slide:!0,"carousel__slide--clone":e.isClone,"carousel__slide--visible":d(),"carousel__slide--active":l(),"carousel__slide--prev":u(),"carousel__slide--next":s(),"carousel__slide--sliding":c.value},"aria-hidden":!d()},null===(r=t.default)||void 0===r?void 0:r.call(t))}}}),S=n("0eaf"),y=n("be89"),C=n("5502"),A={class:"finder"},T=["onClick","src"],_={__name:"finder",setup:function(e){var t=Object(C["b"])(),n=Object(a["computed"])((function(){return t.state.totalPlugins})),i=Object(a["ref"])([]);Object(a["onBeforeMount"])(Object(o["a"])(Object(r["a"])().mark((function e(){return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,S["a"].getFinderDetail();case 2:i.value=e.sent;case 3:case"end":return e.stop()}}),e)}))));var c=Object(a["computed"])((function(){var e=i.value.must||[];return e.length?e.map((function(e){var t=null;return n.value.forEach((function(n){n.name===e&&(t=n)})),t})):[]})),l=function(e){window.rubick.shellOpenExternal(e)},u=Object(a["computed"])((function(){var e=i.value.recommend||[];return e.length?e.map((function(e){var t=null;return n.value.forEach((function(n){n.name===e&&(t=n)})),t})):[]})),s=Object(a["computed"])((function(){var e=i.value.new||[];return e.length?e.map((function(e){var t=null;return n.value.forEach((function(n){n.name===e&&(t=n)})),t})):[]}));return function(e,t){var n=Object(a["resolveComponent"])("a-divider");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",A,[Object(a["createVNode"])(Object(a["unref"])(h),{itemsToShow:2,transition:500},{default:Object(a["withCtx"])((function(){return[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(i.value.banners||[],(function(e,t){return Object(a["openBlock"])(),Object(a["createBlock"])(Object(a["unref"])(k),{key:t},{default:Object(a["withCtx"])((function(){return[Object(a["createElementVNode"])("img",{class:"carousel__item",onClick:function(t){return l(e.link)},src:e.src},null,8,T)]})),_:2},1024)})),128))]})),_:1}),Object(a["createVNode"])(n),Object(a["unref"])(c)&&Object(a["unref"])(c).length?(Object(a["openBlock"])(),Object(a["createBlock"])(y["a"],{key:0,onDownloadSuccess:e.downloadSuccess,title:e.$t("feature.market.finder.must"),list:Object(a["unref"])(c)},null,8,["onDownloadSuccess","title","list"])):Object(a["createCommentVNode"])("",!0),Object(a["unref"])(u)&&Object(a["unref"])(u).length?(Object(a["openBlock"])(),Object(a["createBlock"])(y["a"],{key:1,onDownloadSuccess:e.downloadSuccess,title:e.$t("feature.market.finder.recommended"),list:Object(a["unref"])(u)},null,8,["onDownloadSuccess","title","list"])):Object(a["createCommentVNode"])("",!0),Object(a["unref"])(s)&&Object(a["unref"])(s).length?(Object(a["openBlock"])(),Object(a["createBlock"])(y["a"],{key:2,title:e.$t("feature.market.finder.lastUpdated"),list:Object(a["unref"])(s)},null,8,["title","list"])):Object(a["createCommentVNode"])("",!0)])}}};n("b1f0");const N=_;t["default"]=N},"857a":function(e,t,n){var r=n("e330"),o=n("1d80"),a=n("577e"),i=/"/g,c=r("".replace);e.exports=function(e,t,n,r){var l=a(o(e)),u="<"+t;return""!==n&&(u+=" "+n+'="'+c(a(r),i,"&quot;")+'"'),u+">"+l+"</"+t+">"}},"8cbc":function(e,t,n){},9911:function(e,t,n){"use strict";var r=n("23e7"),o=n("857a"),a=n("af03");r({target:"String",proto:!0,forced:a("link")},{link:function(e){return o(this,"a","href",e)}})},af03:function(e,t,n){var r=n("d039");e.exports=function(e){return r((function(){var t=""[e]('"');return t!==t.toLowerCase()||t.split('"').length>3}))}},b1f0:function(e,t,n){"use strict";n("8cbc")},d81d:function(e,t,n){"use strict";var r=n("23e7"),o=n("b727").map,a=n("1dde"),i=a("map");r({target:"Array",proto:!0,forced:!i},{map:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}})}}]);