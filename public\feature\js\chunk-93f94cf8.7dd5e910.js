(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-93f94cf8"],{"03fe":function(e,t,n){"use strict";n("eb7e")},"07a8":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M878.3 392.1L631.9 145.7c-6.5-6.5-15-9.7-23.5-9.7s-17 3.2-23.5 9.7L423.8 306.9c-12.2-1.4-24.5-2-36.8-2-73.2 0-146.4 24.1-206.5 72.3-15.4 12.3-16.6 35.4-2.7 49.4l181.7 181.7-215.4 215.2a15.8 15.8 0 00-4.6 9.8l-3.4 37.2c-.9 9.4 6.6 17.4 15.9 17.4.5 0 1 0 1.5-.1l37.2-3.4c3.7-.3 7.2-2 9.8-4.6l215.4-215.4 181.7 181.7c6.5 6.5 15 9.7 23.5 9.7 9.7 0 19.3-4.2 25.9-12.4 56.3-70.3 79.7-158.3 70.2-243.4l161.1-161.1c12.9-12.8 12.9-33.8 0-46.8z"}}]},name:"pushpin",theme:"filled"};t.default=c},"45b0":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var c={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M715.8 493.5L335 165.1c-14.2-12.2-35-1.2-35 18.5v656.8c0 19.7 20.8 30.7 35 18.5l380.8-328.4c10.9-9.4 10.9-27.6 0-37z"}}]},name:"caret-right",theme:"outlined"};t.default=c},5609:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=void 0;var c=n("7a23"),r=o(n("b6e7")),a=o(n("a1a0"));function o(e){return e&&e.__esModule?e:{default:e}}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?Object(arguments[t]):{},c=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(c=c.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),c.forEach((function(t){l(e,t,n[t])}))}return e}function l(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var u=function(e,t){var n=i({},e,t.attrs);return(0,c.createVNode)(a["default"],i({},n,{icon:r["default"]}),null)};u.displayName="PushpinOutlined",u.inheritAttrs=!1;var d=u;t["default"]=d},"7db0":function(e,t,n){"use strict";var c=n("23e7"),r=n("b727").find,a=n("44d2"),o="find",i=!0;o in[]&&Array(1)[o]((function(){i=!1})),c({target:"Array",proto:!0,forced:i},{find:function(e){return r(this,e,arguments.length>1?arguments[1]:void 0)}}),a(o)},9903:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=void 0;var c=n("7a23"),r=o(n("45b0")),a=o(n("a1a0"));function o(e){return e&&e.__esModule?e:{default:e}}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?Object(arguments[t]):{},c=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(c=c.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),c.forEach((function(t){l(e,t,n[t])}))}return e}function l(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var u=function(e,t){var n=i({},e,t.attrs);return(0,c.createVNode)(a["default"],i({},n,{icon:r["default"]}),null)};u.displayName="CaretRightOutlined",u.inheritAttrs=!1;var d=u;t["default"]=d},a15b:function(e,t,n){"use strict";var c=n("23e7"),r=n("e330"),a=n("44ad"),o=n("fc6a"),i=n("a640"),l=r([].join),u=a!=Object,d=i("join",",");c({target:"Array",proto:!0,forced:u||!d},{join:function(e){return l(o(this),void 0===e?",":e)}})},a479:function(e,t,n){},a997:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=void 0;var c=n("7a23"),r=o(n("07a8")),a=o(n("a1a0"));function o(e){return e&&e.__esModule?e:{default:e}}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?Object(arguments[t]):{},c=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(c=c.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),c.forEach((function(t){l(e,t,n[t])}))}return e}function l(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var u=function(e,t){var n=i({},e,t.attrs);return(0,c.createVNode)(a["default"],i({},n,{icon:r["default"]}),null)};u.displayName="PushpinFilled",u.inheritAttrs=!1;var d=u;t["default"]=d},b6e7:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M878.3 392.1L631.9 145.7c-6.5-6.5-15-9.7-23.5-9.7s-17 3.2-23.5 9.7L423.8 306.9c-12.2-1.4-24.5-2-36.8-2-73.2 0-146.4 24.1-206.5 72.3a33.23 33.23 0 00-2.7 49.4l181.7 181.7-215.4 215.2a15.8 15.8 0 00-4.6 9.8l-3.4 37.2c-.9 9.4 6.6 17.4 15.9 17.4.5 0 1 0 1.5-.1l37.2-3.4c3.7-.3 7.2-2 9.8-4.6l215.4-215.4 181.7 181.7c6.5 6.5 15 9.7 23.5 9.7 9.7 0 19.3-4.2 25.9-12.4 56.3-70.3 79.7-158.3 70.2-243.4l161.1-161.1c12.9-12.8 12.9-33.8 0-46.8zM666.2 549.3l-24.5 24.5 3.8 34.4a259.92 259.92 0 01-30.4 153.9L262 408.8c12.9-7.1 26.3-13.1 40.3-17.9 27.2-9.4 55.7-14.1 84.7-14.1 9.6 0 19.3.5 28.9 1.6l34.4 3.8 24.5-24.5L608.5 224 800 415.5 666.2 549.3z"}}]},name:"pushpin",theme:"outlined"};t.default=c},c346:function(e,t,n){"use strict";n("fe5b"),n("a479")},e6b9:function(e,t,n){"use strict";n.r(t);var c=n("441f"),r=n.n(c),a=n("a997"),o=n.n(a),i=n("5609"),l=n.n(i),u=n("9903"),d=n.n(u),b=n("c7eb"),s=(n("c346"),n("e63d")),f=n.n(s),O=n("1da1"),j=n("5530"),p=(n("a15b"),n("4de4"),n("d3b7"),n("b0c0"),n("7db0"),n("e9c4"),n("a4d3"),n("e01a"),n("7a23")),m=n("5502"),v=n("6c02"),y=n("df7c"),g=n.n(y),w=n("3c34"),k={class:"installed"},h={class:"view-title"},N={class:"view-container"},V={key:0},x={key:1,class:"container"},_={class:"installed-list"},C=["onClick"],B=["src"],E={class:"info"},P={class:"title"},D={class:"desc"},S={class:"plugin-detail"},M={class:"plugin-top"},L={class:"left"},T={class:"title"},z={class:"desc"},A={class:"desc"},F={class:"right"},R={class:"feature-container"},q={key:0,class:"desc-item"},J={__name:"index",setup:function(e){var t,n=window.require("electron"),c=(n.ipcRenderer,window.require("@electron/remote")),a=(window.require("fs"),c.app.getPath("userData")),i=(g.a.join(a,"./rubick-plugins"),Object(m["b"])()),u=Object(v["c"])(),s=Object(v["d"])(),y=Object(p["computed"])((function(){return i.state.localPlugins.filter((function(e){return"rubick-system-feature"!==e.name}))})),J=function(){return i.dispatch("updateLocalPlugin")},$=function(e){return i.dispatch("startUnDownload",e)},U=function(e){return i.dispatch("errorUnDownload",e)},I=Object(p["ref"])([u.query.plugin||(null===y||void 0===y||null===(t=y.value[0])||void 0===t?void 0:t.name)]);Object(p["watch"])(y,(function(){var e;I.value=[null===y||void 0===y||null===(e=y.value[0])||void 0===e?void 0:e.name]}));var G=Object(p["computed"])((function(){return y.value.find((function(e){return e.name===I.value[0]}))||{}})),H=Object(p["ref"])(window.rubick.db.get("super-panel-user-plugins")||{data:[],_id:"super-panel-user-plugins"}),K=function(e,t,n){"open"===e?Y({code:t.code,cmd:n}):"add"===e?Q({cmd:n,code:t.code}):W({cmd:n,name:t.name})},Q=function(e){var t=e.cmd,n=e.code,c=Object(j["a"])(Object(j["a"])({},Object(p["toRaw"])(G.value)),{},{cmd:t,ext:{code:n,type:"text",payload:null}});H.value.data.push(c),window.rubick.db.put(Object(p["toRaw"])(H.value))},W=function(e){var t=e.cmd,n=e.name;H.value.data=Object(p["toRaw"])(H.value).data.filter((function(e){return n?e.name!==n:e.cmd!==t})),window.rubick.db.put(Object(p["toRaw"])(H.value))},X=function(e){var t=!1;return H.value.data.some((function(n){return n.cmd===e&&(t=!0,!0)})),t},Y=function(e){var t=e.cmd,n=e.code;window.rubick.openPlugin(JSON.parse(JSON.stringify(Object(j["a"])(Object(j["a"])({},G.value),{},{cmd:t,ext:{code:n,type:"text",payload:null}}))))},Z=function(){var e=Object(O["a"])(Object(b["a"])().mark((function e(t){var n;return Object(b["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return $(t.name),n=setTimeout((function(){U(t.name),f.a.error("卸载超时，请重试！")}),2e4),e.next=4,window.market.deletePlugin(t);case 4:W({name:t.name}),J(),clearTimeout(n);case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),ee=function(){s.push("/finder"),i.commit("commonUpdate",{active:["finder"]})};return function(e,t){var n=Object(p["resolveComponent"])("Vue3Lottie"),c=Object(p["resolveComponent"])("a-button"),a=Object(p["resolveComponent"])("a-result"),i=Object(p["resolveComponent"])("a-tag"),u=Object(p["resolveComponent"])("a-menu-item"),b=Object(p["resolveComponent"])("a-menu"),s=Object(p["resolveComponent"])("a-dropdown");return Object(p["openBlock"])(),Object(p["createElementBlock"])("div",k,[Object(p["createElementVNode"])("div",h,Object(p["toDisplayString"])(e.$t("feature.installed.title")),1),Object(p["createElementVNode"])("div",N,[Object(p["unref"])(y).length?(Object(p["openBlock"])(),Object(p["createElementBlock"])("div",x,[Object(p["createElementVNode"])("div",_,[(Object(p["openBlock"])(!0),Object(p["createElementBlock"])(p["Fragment"],null,Object(p["renderList"])(Object(p["unref"])(y),(function(e,t){return Object(p["openBlock"])(),Object(p["createElementBlock"])("div",{class:Object(p["normalizeClass"])(I.value[0]===e.name?"item active":"item"),key:t,onClick:function(t){return I.value=[e.name]}},[Object(p["createElementVNode"])("img",{src:e.logo},null,8,B),Object(p["createElementVNode"])("div",E,[Object(p["createElementVNode"])("div",P,Object(p["toDisplayString"])(e.pluginName),1),Object(p["createElementVNode"])("div",D,Object(p["toDisplayString"])(e.description),1)])],10,C)})),128))]),Object(p["createElementVNode"])("div",S,[Object(p["createElementVNode"])("div",M,[Object(p["createElementVNode"])("div",L,[Object(p["createElementVNode"])("div",T,[Object(p["createTextVNode"])(Object(p["toDisplayString"])(Object(p["unref"])(G).pluginName)+" ",1),Object(p["createVNode"])(i,null,{default:Object(p["withCtx"])((function(){return[Object(p["createTextVNode"])(Object(p["toDisplayString"])(Object(p["unref"])(G).version),1)]})),_:1})]),Object(p["createElementVNode"])("div",z,Object(p["toDisplayString"])(e.$t("feature.installed.developer"))+"："+Object(p["toDisplayString"])("".concat(Object(p["unref"])(G).author||e.$t("feature.installed.unknown"))),1),Object(p["createElementVNode"])("div",A,Object(p["toDisplayString"])(Object(p["unref"])(G).description),1)]),Object(p["createElementVNode"])("div",F,[Object(p["createVNode"])(c,{type:"primary",size:"small",shape:"round",loading:Object(p["unref"])(G).isloading,onClick:t[0]||(t[0]=function(e){return Z(Object(p["unref"])(G))})},{default:Object(p["withCtx"])((function(){return[Object(p["createTextVNode"])(Object(p["toDisplayString"])(e.$t("feature.installed.remove")),1)]})),_:1},8,["loading"])])]),Object(p["createElementVNode"])("div",R,[(Object(p["openBlock"])(!0),Object(p["createElementBlock"])(p["Fragment"],null,Object(p["renderList"])(Object(p["unref"])(G).features,(function(e,t){return Object(p["openBlock"])(),Object(p["createElementBlock"])(p["Fragment"],{key:t},[e.cmds.filter((function(e){return!e.label})).length>0?(Object(p["openBlock"])(),Object(p["createElementBlock"])("div",q,[Object(p["createElementVNode"])("div",null,Object(p["toDisplayString"])(e.explain),1),(Object(p["openBlock"])(!0),Object(p["createElementBlock"])(p["Fragment"],null,Object(p["renderList"])(e.cmds,(function(t){return Object(p["openBlock"])(),Object(p["createElementBlock"])(p["Fragment"],{key:t},[t.label?Object(p["createCommentVNode"])("",!0):(Object(p["openBlock"])(),Object(p["createBlock"])(s,{key:0,class:Object(p["normalizeClass"])({executable:!t.label})},{overlay:Object(p["withCtx"])((function(){return[Object(p["createVNode"])(b,{onClick:function(n){var c=n.key;return K(c,e,t)}},{default:Object(p["withCtx"])((function(){return[Object(p["createVNode"])(u,{key:"open"},{default:Object(p["withCtx"])((function(){return[Object(p["createVNode"])(Object(p["unref"])(d.a)),Object(p["createTextVNode"])(" 运行 ")]})),_:1}),X(t)?(Object(p["openBlock"])(),Object(p["createBlock"])(u,{key:"remove"},{default:Object(p["withCtx"])((function(){return[Object(p["createVNode"])(Object(p["unref"])(o.a)),Object(p["createTextVNode"])(" 从超级面板中取消固定 ")]})),_:1})):(Object(p["openBlock"])(),Object(p["createBlock"])(u,{key:"add"},{default:Object(p["withCtx"])((function(){return[Object(p["createVNode"])(Object(p["unref"])(l.a)),Object(p["createTextVNode"])(" 固定到超级面板 ")]})),_:1}))]})),_:2},1032,["onClick"])]})),default:Object(p["withCtx"])((function(){return[Object(p["createVNode"])(c,{size:"small",class:"keyword-tag"},{default:Object(p["withCtx"])((function(){return[Object(p["createTextVNode"])(Object(p["toDisplayString"])(t.label||t)+" ",1),Object(p["createVNode"])(Object(p["unref"])(r.a))]})),_:2},1024)]})),_:2},1032,["class"]))],64)})),128))])):Object(p["createCommentVNode"])("",!0)],64)})),128))])])])):(Object(p["openBlock"])(),Object(p["createElementBlock"])("div",V,[Object(p["createVNode"])(a,{class:"error-content","sub-title":"哎呀，暂时还没有安装任何插件！"},{icon:Object(p["withCtx"])((function(){return[Object(p["createVNode"])(n,{animationData:Object(p["unref"])(w),height:240,width:240},null,8,["animationData"])]})),extra:Object(p["withCtx"])((function(){return[Object(p["createVNode"])(c,{onClick:ee,key:"console",type:"primary"},{default:Object(p["withCtx"])((function(){return[Object(p["createTextVNode"])("去插件市场看看吧")]})),_:1})]})),_:1})]))])])}}},$=(n("03fe"),n("6b0d")),U=n.n($);const I=U()(J,[["__scopeId","data-v-1ed304de"]]);t["default"]=I},eb7e:function(e,t,n){}}]);