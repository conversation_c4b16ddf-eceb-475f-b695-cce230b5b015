{"name": "rubick", "version": "4.3.5", "author": "muwoo <<EMAIL>>", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "feature:dev": "cd feature & npm run serve", "electron:build": "vue-cli-service electron:build", "electron:serve": "vue-cli-service electron:serve", "release": "vue-cli-service electron:build", "postinstall": "electron-builder install-app-deps", "postuninstall": "electron-builder install-app-deps"}, "main": "index.js", "optionalDependencies": {"electron-clipboard-ex": "^1.3.3"}, "dependencies": {"@better-scroll/core": "^2.4.2", "@electron/remote": "^2.0.10", "ant-design-vue": "3.2.14", "axios": "^1.3.4", "core-js": "^3.6.5", "cross-spawn": "^7.0.3", "electron-updater": "^4.6.5", "extract-file-icon": "^0.3.2", "fix-path": "^3.0.0", "get-mac-apps": "^1.0.2", "got": "^11.8.3", "lodash.debounce": "^4.0.8", "memorystream": "^0.3.1", "node-key-sender": "^1.0.11", "npm": "6.14.7", "pinyin-match": "^1.2.4", "pouchdb": "^7.2.2", "pouchdb-load": "^1.4.6", "pouchdb-replication-stream": "^1.2.9", "simple-plist": "0.2.1", "uiohook-napi": "^1.5.4", "vue": "^3.0.0", "vue-router": "^4.0.0-0", "vuex": "^4.0.0-0", "webdav": "4.11.3", "worker-loader": "^3.0.8"}, "devDependencies": {"@electron/asar": "^3.2.8", "@ts-type/package-dts": "^1.0.53", "@types/electron-devtools-installer": "^2.2.0", "@typescript-eslint/eslint-plugin": "^4.18.0", "@typescript-eslint/parser": "^4.18.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^7.0.0", "babel-plugin-import": "^1.13.3", "compressing": "^1.10.0", "electron": "26.0.0", "electron-builder": "22.13.1", "electron-devtools-installer": "^3.1.0", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^7.0.0", "less": "^3.0.4", "less-loader": "^5.0.0", "prettier": "^2.8.4", "typescript": "~4.1.5", "vue-cli-plugin-electron-builder": "3.0.0-alpha.4", "worker-plugin": "^5.0.1"}, "pnpm": {"overrides": {"electron-builder": "^23.0.3", "leveldown": "6.0.3"}}}