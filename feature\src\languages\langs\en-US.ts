export default {
  'en-US': {
    feature: {
      market: {
        title: 'Market',
        search: 'Search Plugins',
        searchResult: 'Search Results',
        explore: 'Explore',
        efficiency: 'Efficiency',
        searchTool: 'Search Tools',
        imageTool: 'Image Tools',
        developTool: 'Develop Tools',
        systemTool: 'System Tools',
        finder: {
          must: 'Necessary',
          recommended: 'Recommended',
          lastUpdated: 'Total',
        },
        install: 'Install',
      },
      installed: {
        title: 'Installed',
        tips1: 'There are no plug-ins.',
        tips2: 'Go to the plugin market and choose the plugin to install!',
        developer: 'Developer',
        unknown: 'Unknown',
        remove: 'Remove',
        functionKey: 'Function Key',
        detailInfo: 'Detail Info',
        addToPanel: 'Click the + sign to pin the keyword to the super panel',
        removeFromPanel:
          'Click the - sign to remove the keyword from the super panel',
      },
      settings: {
        title: 'Account And Setting',
        account: {
          accountInfo: 'Account Info',
          tips1: 'rubick',
          tips2:
            'After the software preferences are set, please restart the software. Please go to the mini program set avatar and nickname.',
          themeColor: 'Theme Setting',
          spring: 'Spring',
          summer: 'Summer',
          autumn: 'Autumn',
          winter: 'Winter',
          personalized: 'Personalized',
          greeting: 'Search Box Greeting',
          logo: 'Avatar',
          replace: 'Repalce Logo',
          reset: 'Reset Default',
          name: 'User Name',
        },
        basic: {
          title: 'Basic',
          shortcutKey: 'Shortcut Key',
          showOrHiddle: 'Show Or Hiddle',
          screenCapture: 'Screen Capture',
          common: 'Common',
          autoPaste: 'Auto Paste',
          autoBoot: 'Auto Boot Up',
          spaceExec: 'Space Execution',
          on: 'on',
          off: 'off',
          theme: 'Theme',
          darkMode: 'Dark Mode',
          language: 'Language',
          changeLang: 'Change Language',
          cn: '简体中文',
          en: 'English',
          history: 'keywords search history',
        },
        global: {
          title: 'Global Shortcut Key',
          instructions: 'Instructions and examples',
          tips: 'Press the shortcut key, automatically search for the corresponding keyword, when the keyword result is exactly matched, and the result is unique, it will point directly to this function.',
          example: 'Example',
          example1: 'Shortcut key 「Alt + W」 keyword 「Wechat」',
          tips1: 'Press 「Alt + W」 to open the local Wechat app directly',
          example2: 'Shortcut key 「Ctrl + Alt + A」 keyword 「screenshot」',
          tips2: 'Press 「Ctrl + Alt + A」 to take a screenshot',
          shortcutKey: 'Shortcut Key',
          funtionKey: 'Funtion Key',
          addShortcutKey: 'ADD Global Shortcut Key',
          addShortcutKeyTips:
            'Press the function keys (Ctrl, Shift, {optionKeyName}) first, and then press other normal keys. Or press the F1-F12 button.',
        },
        superPanel: {
          title: 'Super Panel',
          tips: 'Please select the common plug-ins that need to be added to the super panel.',
          add: 'Add',
          remove: 'Revome',
        },
        intranet: {
          title: 'Intranet Deployment',
          tips: "If publishing plug-ins to the public network npm does not meet your company's security requirements, rubick supports private network private sources and private plug-in libraries. If you need private network deployment, you can configure the following rules.",
          npmMirror: 'npm mirror',
          dbUrl: 'database url',
          accessToken: 'access token',
          placeholder: 'required for private network gitlab warehouse',
        },
        localstart: {
          title: 'Local Start',
        },
        database: {
          title: 'Data Synchronization',
        },
      },
      dev: {
        title: 'Developer',
        tips: 'The rubick plug-in system relies on npm management. Local debugging needs to first execute npm link in the current directory of the local plug-in.',
        pluginName: 'Plugin Name',
        install: 'Install',
        refreshPlugins: 'Refresh Plugins',
        installSuccess: '{pluginName} Install Successed!',
        refreshSuccess: '{pluginName} Refresh Successed!',
      },
    },
  },
};
