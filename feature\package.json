{"name": "feature", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --port 8081", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@ant-design/icons-vue": "^6.0.1", "@vue/cli-service": "~4.5.0", "ant-design-vue": "3.2.14", "axios": "^0.24.0", "babel-plugin-import": "^1.13.8", "core-js": "^3.6.5", "lodash.debounce": "^4.0.8", "lodash.throttle": "^4.1.1", "markdown-it": "^12.2.0", "nanoid": "^4.0.2", "vue": "3.2.45", "vue-i18n": "9.2.2", "vue-router": "^4.0.0-0", "vue3-carousel": "^0.3.1", "vue3-lottie": "^3.1.0", "vuex": "^4.0.0-0", "webpack-bundle-analyzer": "^4.9.1"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^4.18.0", "@typescript-eslint/parser": "^4.18.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^7.0.0", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^7.0.0", "less": "^4.1.3", "less-loader": "5.0.0", "prettier": "^2.2.1", "typescript": "~4.1.5"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended", "@vue/typescript/recommended", "@vue/prettier", "@vue/prettier/@typescript-eslint"], "parserOptions": {"ecmaVersion": 2020}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}