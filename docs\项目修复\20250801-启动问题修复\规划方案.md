# Rubick 项目启动问题修复 - 规划方案

## 项目概述

**项目名称**: Rubick 启动问题诊断与修复  
**修复日期**: 2025年8月1日  
**问题描述**: 项目无法正常启动，使用 `pnpm run electron:serve` 命令时出现依赖缺失和类型错误  

## 问题分析

### 1. 主要问题识别

通过初步诊断，发现以下关键问题：

#### 1.1 依赖包缺失
- **plist**: 在 `src/common/utils/getCopyFiles.ts` 和 `src/core/app-search/get-mac-app/getApps.ts` 中被引用但未安装
- **fs-extra**: 在 `src/core/plugin-handler/index.ts` 中被引用但未安装  
- **@ant-design/icons-vue**: 在搜索组件中被引用但未正确安装

#### 1.2 TypeScript 类型错误
- 在 `src/renderer/plugins-manager/options.ts` 中存在 `never[]` 类型推断问题
- Vue 3 的 `ref` 类型推断不正确，导致无法访问 `value` 属性

#### 1.3 包管理器配置问题
- 项目已部分迁移到 pnpm，但依赖安装不完整
- 存在 webpack 版本兼容性问题（webpack 4 与 webpack 5 冲突）

### 2. 根本原因分析

1. **依赖管理不一致**: 项目在从 npm 迁移到 pnpm 过程中，部分依赖未正确安装
2. **类型定义过时**: Vue 3 早期版本的类型定义存在兼容性问题
3. **构建工具版本冲突**: `vue-cli-plugin-electron-builder` 版本较老，与新版本 webpack 存在兼容性问题

## 解决方案设计

### 阶段一：依赖包修复
1. 使用 pnpm 安装缺失的运行时依赖：`plist`、`fs-extra`、`@ant-design/icons-vue`
2. 安装对应的 TypeScript 类型声明：`@types/plist`、`@types/fs-extra`
3. 清理并重新安装所有依赖以解决版本冲突

### 阶段二：类型错误修复
1. 修复 `src/renderer/plugins-manager/options.ts` 中的类型定义
2. 为 `optionsRef` 提供正确的类型注解
3. 使用类型断言解决 Vue 3 类型推断问题

### 阶段三：构建配置优化
1. 优化 pnpm 配置，确保依赖正确解析
2. 处理 webpack 版本兼容性问题
3. 验证项目能够正常启动

### 阶段四：测试验证
1. 执行 `pnpm run electron:serve` 命令验证启动
2. 确认 TypeScript 编译无错误
3. 验证基本功能可用性

## 预期结果

1. **依赖问题解决**: 所有缺失的依赖包正确安装
2. **类型错误消除**: TypeScript 编译通过，无类型错误
3. **项目正常启动**: `electron:serve` 命令能够成功执行
4. **包管理器迁移完成**: 完全使用 pnpm 作为包管理器

## 风险评估

### 低风险
- 依赖包安装：标准操作，风险较低
- 类型错误修复：局部修改，影响范围可控

### 中等风险
- webpack 版本兼容性：可能需要额外配置调整
- Vue 版本升级：可能影响其他组件的类型定义

### 缓解措施
1. 在修改前备份关键文件
2. 逐步验证每个修复步骤
3. 保持现有功能的向后兼容性

## 成功标准

1. ✅ 项目能够使用 `pnpm run electron:serve` 正常启动
2. ✅ TypeScript 编译无错误（显示 "No issues found"）
3. ✅ 所有缺失依赖正确安装
4. ✅ 基本的 Electron 应用功能可用

## 后续建议

1. **定期依赖更新**: 建立定期更新依赖的流程
2. **类型检查增强**: 在 CI/CD 中加入严格的类型检查
3. **构建工具升级**: 考虑升级到更新版本的 electron-builder 插件
4. **文档完善**: 更新项目的开发环境搭建文档
