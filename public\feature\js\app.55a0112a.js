(function(e){function t(t){for(var c,a,u=t[0],i=t[1],s=t[2],l=0,d=[];l<u.length;l++)a=u[l],Object.prototype.hasOwnProperty.call(r,a)&&r[a]&&d.push(r[a][0]),r[a]=0;for(c in i)Object.prototype.hasOwnProperty.call(i,c)&&(e[c]=i[c]);f&&f(t);while(d.length)d.shift()();return o.push.apply(o,s||[]),n()}function n(){for(var e,t=0;t<o.length;t++){for(var n=o[t],c=!0,a=1;a<n.length;a++){var u=n[a];0!==r[u]&&(c=!1)}c&&(o.splice(t--,1),e=i(i.s=n[0]))}return e}var c={},a={app:0},r={app:0},o=[];function u(e){return i.p+"js/"+({}[e]||e)+"."+{"chunk-197bc330":"95c96fee","chunk-2d0c49e7":"20196ed0","chunk-93f94cf8":"7dd5e910","chunk-3fb623ce":"ee432c33","chunk-111de227":"86b2c5e3","chunk-1af68a6e":"2022b1a8","chunk-0add5e88":"26781f59","chunk-596c6184":"a1398c77","chunk-a2b32e48":"20d42be1","chunk-cf61e458":"20835e3d","chunk-f69c766e":"230d2fbd","chunk-46496dac":"719cab20","chunk-88e69aa8":"55955c4d"}[e]+".js"}function i(t){if(c[t])return c[t].exports;var n=c[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,i),n.l=!0,n.exports}i.e=function(e){var t=[],n={"chunk-197bc330":1,"chunk-93f94cf8":1,"chunk-3fb623ce":1,"chunk-111de227":1,"chunk-1af68a6e":1,"chunk-0add5e88":1,"chunk-596c6184":1,"chunk-a2b32e48":1,"chunk-cf61e458":1,"chunk-f69c766e":1,"chunk-46496dac":1,"chunk-88e69aa8":1};a[e]?t.push(a[e]):0!==a[e]&&n[e]&&t.push(a[e]=new Promise((function(t,n){for(var c="css/"+({}[e]||e)+"."+{"chunk-197bc330":"29812a6a","chunk-2d0c49e7":"31d6cfe0","chunk-93f94cf8":"0f63dd4a","chunk-3fb623ce":"2b9b7de2","chunk-111de227":"2450c00b","chunk-1af68a6e":"2450c00b","chunk-0add5e88":"2f22e37d","chunk-596c6184":"9be2495c","chunk-a2b32e48":"67429700","chunk-cf61e458":"9be2495c","chunk-f69c766e":"9be2495c","chunk-46496dac":"da060b5a","chunk-88e69aa8":"69c3b24d"}[e]+".css",r=i.p+c,o=document.getElementsByTagName("link"),u=0;u<o.length;u++){var s=o[u],l=s.getAttribute("data-href")||s.getAttribute("href");if("stylesheet"===s.rel&&(l===c||l===r))return t()}var d=document.getElementsByTagName("style");for(u=0;u<d.length;u++){s=d[u],l=s.getAttribute("data-href");if(l===c||l===r)return t()}var f=document.createElement("link");f.rel="stylesheet",f.type="text/css",f.onload=t,f.onerror=function(t){var c=t&&t.target&&t.target.src||r,o=new Error("Loading CSS chunk "+e+" failed.\n("+c+")");o.code="CSS_CHUNK_LOAD_FAILED",o.request=c,delete a[e],f.parentNode.removeChild(f),n(o)},f.href=r;var p=document.getElementsByTagName("head")[0];p.appendChild(f)})).then((function(){a[e]=0})));var c=r[e];if(0!==c)if(c)t.push(c[2]);else{var o=new Promise((function(t,n){c=r[e]=[t,n]}));t.push(c[2]=o);var s,l=document.createElement("script");l.charset="utf-8",l.timeout=120,i.nc&&l.setAttribute("nonce",i.nc),l.src=u(e);var d=new Error;s=function(t){l.onerror=l.onload=null,clearTimeout(f);var n=r[e];if(0!==n){if(n){var c=t&&("load"===t.type?"missing":t.type),a=t&&t.target&&t.target.src;d.message="Loading chunk "+e+" failed.\n("+c+": "+a+")",d.name="ChunkLoadError",d.type=c,d.request=a,n[1](d)}r[e]=void 0}};var f=setTimeout((function(){s({type:"timeout",target:l})}),12e4);l.onerror=l.onload=s,document.head.appendChild(l)}return Promise.all(t)},i.m=e,i.c=c,i.d=function(e,t,n){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},i.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var c in e)i.d(n,c,function(t){return e[t]}.bind(null,c));return n},i.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i.oe=function(e){throw console.error(e),e};var s=window["webpackJsonp"]=window["webpackJsonp"]||[],l=s.push.bind(s);s.push=t,s=s.slice();for(var d=0;d<s.length;d++)t(s[d]);var f=l;o.push([0,"chunk-vendors"]),n()})({0:function(e,t,n){e.exports=n("cd49")},"0eaf":function(e,t,n){"use strict";var c=n("c7eb"),a=n("1da1"),r=(n("99af"),n("bc3a")),o=n.n(r),u="https://gitee.com/monkeyWang/rubickdatabase/raw/master",i="";try{var s=window.rubick.db.get("rubick-localhost-config");u=s.data.database,i=s.data.access_token}catch(d){}var l=o.a.create({timeout:4e3,baseURL:u||"https://gitee.com/monkeyWang/rubickdatabase/raw/master"});t["a"]={getTotalPlugins:function(){return Object(a["a"])(Object(c["a"])().mark((function e(){var t,n;return Object(c["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t="plugins/total-plugins.json",i&&(t="".concat(encodeURIComponent(t),"/raw?access_token=").concat(i,"&ref=master")),e.next=4,l.get(t);case 4:return n=e.sent,e.abrupt("return",n.data);case 6:case"end":return e.stop()}}),e)})))()},getFinderDetail:function(){return Object(a["a"])(Object(c["a"])().mark((function e(){var t,n;return Object(c["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t="plugins/finder.json",i&&(t="".concat(encodeURIComponent(t),"/raw?access_token=").concat(i,"&ref=master")),e.next=4,l.get(t);case 4:return n=e.sent,e.abrupt("return",n.data);case 6:case"end":return e.stop()}}),e)})))()},getSystemDetail:function(){return Object(a["a"])(Object(c["a"])().mark((function e(){var t,n;return Object(c["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t="plugins/system.json",i&&(t="".concat(encodeURIComponent(t),"/raw?access_token=").concat(i,"&ref=master")),e.next=4,l.get(t);case 4:return n=e.sent,e.abrupt("return",n.data);case 6:case"end":return e.stop()}}),e)})))()},getWorkerDetail:function(){return Object(a["a"])(Object(c["a"])().mark((function e(){var t,n;return Object(c["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t="plugins/worker.json",i&&(t="".concat(encodeURIComponent(t),"/raw?access_token=").concat(i,"&ref=master")),e.next=4,l.get(t);case 4:return n=e.sent,e.abrupt("return",n.data);case 6:case"end":return e.stop()}}),e)})))()},getPluginDetail:function(e){return Object(a["a"])(Object(c["a"])().mark((function t(){var n;return Object(c["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,l.get(e);case 2:return n=t.sent,t.abrupt("return",n.data);case 4:case"end":return t.stop()}}),t)})))()},getSearchDetail:function(){return Object(a["a"])(Object(c["a"])().mark((function e(){var t,n;return Object(c["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t="plugins/search.json",i&&(t="".concat(encodeURIComponent(t),"/raw?access_token=").concat(i,"&ref=master")),e.next=4,l.get(t);case 4:return n=e.sent,e.abrupt("return",n.data);case 6:case"end":return e.stop()}}),e)})))()},getDevDetail:function(){return Object(a["a"])(Object(c["a"])().mark((function e(){var t,n;return Object(c["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t="plugins/dev.json",i&&(t="".concat(encodeURIComponent(t),"/raw?access_token=").concat(i,"&ref=master")),e.next=4,l.get(t);case 4:return n=e.sent,e.abrupt("return",n.data);case 6:case"end":return e.stop()}}),e)})))()},getImageDetail:function(){return Object(a["a"])(Object(c["a"])().mark((function e(){var t,n;return Object(c["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t="plugins/image.json",i&&(t="".concat(encodeURIComponent(t),"/raw?access_token=").concat(i,"&ref=master")),e.next=4,l.get(t);case 4:return n=e.sent,e.abrupt("return",n.data);case 6:case"end":return e.stop()}}),e)})))()}}},"375b":function(e,t,n){},"60b1":function(e,t,n){"use strict";var c=n("5530"),a="rubick-local-config",r={getConfig:function(){var e=window.rubick.db.get(a)||{};return e.data},setConfig:function(e){var t=window.rubick.db.get(a)||{};window.rubick.db.put({_id:a,_rev:t._rev,data:Object(c["a"])(Object(c["a"])({},t.data),e)})}};t["a"]=r},6378:function(e,t,n){"use strict";n("9354")},9354:function(e,t,n){},c67a:function(e,t,n){},cd49:function(e,t,n){"use strict";n.r(t);n("95d1");var c=n("c4c6"),a=n.n(c),r=(n("cb19"),n("b6c0")),o=n.n(r),u=(n("12eb"),n("257f")),i=n.n(u),s=(n("0c4c"),n("fed8")),l=n.n(s),d=(n("10c0"),n("4796")),f=n.n(d),p=(n("387f5"),n("a086")),b=n.n(p),m=(n("ad26"),n("92df")),h=n.n(m),g=(n("1325"),n("57df")),k=n.n(g),O=(n("757f"),n("c05c")),j=n.n(O),w=(n("cf0b"),n("8f58")),v=n.n(w),y=(n("99bf"),n("f71e")),x=n.n(y),P=(n("6c6d"),n("5023")),C=n.n(P),S=(n("734c"),n("91f7")),N=n.n(S),T=(n("bd1c"),n("76b8")),V=n.n(T),_=(n("aa11"),n("edb7")),D=n.n(_),A=(n("c279"),n("33ba")),E=n.n(A),U=(n("1e62"),n("073a")),I=n.n(U),K=(n("cd09"),n("7531")),L=n.n(K),R=(n("3a22"),n("1d802")),z=n.n(R),B=(n("f27b"),n("e3db")),F=n.n(B),M=(n("7e5c"),n("7939")),$=n.n(M),W=(n("e260"),n("e6cf"),n("cca6"),n("a79d"),n("7a23")),J=n("c832"),q=n("d3f6"),G=n.n(q),H=n("0187"),Q=n.n(H),X=n("26f2"),Y=n.n(X),Z=n("b77f"),ee=n.n(Z),te=n("50a0"),ne=n.n(te),ce=n("78b7"),ae=n.n(ce),re=n("133c"),oe=n.n(re),ue=n("4737"),ie=n.n(ue),se=n("ba42"),le=n.n(se),de=(n("caad"),n("6c02")),fe=n("5502"),pe=n("60b1"),be={class:"main-container"},me={class:"left-menu"},he=["src"],ge=Object(W["defineComponent"])({__name:"App",setup:function(e){var t=Object(fe["b"])(),n=Object(de["d"])(),c=Object(W["computed"])((function(){return t.state.active})),a=pe["a"].getConfig(),r=a.perf,o=function(e){t.commit("commonUpdate",{active:[e]}),n.push(e)};window.rubick.onPluginEnter((function(e){var n=e.code;o(n),t.commit("commonUpdate",{active:[n]})})),window.rubick.setSubInput((function(e){["finder","result","devPlugin","image","tools","worker","system"].includes(c.value[0])&&(e.text?(t.commit("setSearchValue",e.text),n.push("result")):(t.commit("commonUpdate",{active:["finder"]}),n.push("finder")))}),"搜索插件");var u=function(){return t.dispatch("init")};return u(),function(e,t){var n=Object(W["resolveComponent"])("a-menu-item"),a=Object(W["resolveComponent"])("a-avatar"),u=Object(W["resolveComponent"])("a-sub-menu"),i=Object(W["resolveComponent"])("a-menu"),s=Object(W["resolveComponent"])("router-view");return Object(W["openBlock"])(),Object(W["createElementBlock"])("div",be,[Object(W["createElementVNode"])("div",me,[Object(W["createVNode"])(i,{onSelect:t[0]||(t[0]=function(e){var t=e.key;return o(t)}),selectedKeys:Object(W["unref"])(c),mode:"vertical"},{default:Object(W["withCtx"])((function(){return[Object(W["createVNode"])(n,{key:"finder"},{icon:Object(W["withCtx"])((function(){return[Object(W["createVNode"])(Object(W["unref"])(le.a),{style:{"font-size":"18px"}})]})),default:Object(W["withCtx"])((function(){return[Object(W["createTextVNode"])(" "+Object(W["toDisplayString"])(e.$t("feature.market.explore")),1)]})),_:1}),Object(W["createVNode"])(n,{key:"worker"},{icon:Object(W["withCtx"])((function(){return[Object(W["createVNode"])(Object(W["unref"])(ie.a),{style:{transform:"rotate(-45deg)","font-size":"18px"}})]})),default:Object(W["withCtx"])((function(){return[Object(W["createTextVNode"])(" "+Object(W["toDisplayString"])(e.$t("feature.market.efficiency")),1)]})),_:1}),Object(W["createVNode"])(n,{key:"tools"},{icon:Object(W["withCtx"])((function(){return[Object(W["createVNode"])(Object(W["unref"])(oe.a),{style:{"font-size":"18px"}})]})),default:Object(W["withCtx"])((function(){return[Object(W["createTextVNode"])(" "+Object(W["toDisplayString"])(e.$t("feature.market.searchTool")),1)]})),_:1}),Object(W["createVNode"])(n,{key:"image"},{icon:Object(W["withCtx"])((function(){return[Object(W["createVNode"])(Object(W["unref"])(ae.a),{style:{"font-size":"18px"}})]})),default:Object(W["withCtx"])((function(){return[Object(W["createTextVNode"])(" "+Object(W["toDisplayString"])(e.$t("feature.market.imageTool")),1)]})),_:1}),Object(W["createVNode"])(n,{key:"devPlugin"},{icon:Object(W["withCtx"])((function(){return[Object(W["createVNode"])(Object(W["unref"])(ne.a),{style:{"font-size":"18px"}})]})),default:Object(W["withCtx"])((function(){return[Object(W["createTextVNode"])(" "+Object(W["toDisplayString"])(e.$t("feature.market.developTool")),1)]})),_:1}),Object(W["createVNode"])(n,{key:"system"},{icon:Object(W["withCtx"])((function(){return[Object(W["createVNode"])(Object(W["unref"])(ee.a),{style:{"font-size":"18px"}})]})),default:Object(W["withCtx"])((function(){return[Object(W["createTextVNode"])(" "+Object(W["toDisplayString"])(e.$t("feature.market.systemTool")),1)]})),_:1}),Object(W["createVNode"])(u,{class:"user-info"},{icon:Object(W["withCtx"])((function(){return[Object(W["createVNode"])(a,{size:32},{icon:Object(W["withCtx"])((function(){return[Object(W["createElementVNode"])("img",{src:Object(W["unref"])(r).custom.logo},null,8,he)]})),_:1})]})),title:Object(W["withCtx"])((function(){return[Object(W["createTextVNode"])(Object(W["toDisplayString"])(Object(W["unref"])(r).custom.username),1)]})),default:Object(W["withCtx"])((function(){return[Object(W["createVNode"])(n,{key:"settings"},{icon:Object(W["withCtx"])((function(){return[Object(W["createVNode"])(Object(W["unref"])(Y.a))]})),default:Object(W["withCtx"])((function(){return[Object(W["createTextVNode"])(" "+Object(W["toDisplayString"])(e.$t("feature.settings.title")),1)]})),_:1}),Object(W["createVNode"])(n,{key:"installed"},{icon:Object(W["withCtx"])((function(){return[Object(W["createVNode"])(Object(W["unref"])(Q.a))]})),default:Object(W["withCtx"])((function(){return[Object(W["createTextVNode"])(" "+Object(W["toDisplayString"])(e.$t("feature.installed.title")),1)]})),_:1}),Object(W["createVNode"])(n,{key:"dev"},{icon:Object(W["withCtx"])((function(){return[Object(W["createVNode"])(Object(W["unref"])(G.a))]})),default:Object(W["withCtx"])((function(){return[Object(W["createTextVNode"])(" "+Object(W["toDisplayString"])(e.$t("feature.dev.title")),1)]})),_:1})]})),_:1})]})),_:1},8,["selectedKeys"])]),Object(W["createElementVNode"])("div",{class:Object(W["normalizeClass"])(["finder","result","devPlugin","image","tools","worker","system"].includes(Object(W["unref"])(c)[0])?"container":"more")},[(Object(W["openBlock"])(),Object(W["createBlock"])(W["KeepAlive"],null,[Object(W["createVNode"])(s)],1024))],2)])}}}),ke=(n("f17b"),n("6378"),n("6b0d")),Oe=n.n(ke);const je=Oe()(ge,[["__scopeId","data-v-435a1273"]]);var we=je,ve=(n("d3b7"),n("3ca3"),n("ddb0"),[{path:"/result",name:"result",component:function(){return Promise.all([n.e("chunk-3fb623ce"),n.e("chunk-2d0c49e7"),n.e("chunk-0add5e88")]).then(n.bind(null,"a272"))}},{path:"/devPlugin",name:"devPlugin",component:function(){return Promise.all([n.e("chunk-3fb623ce"),n.e("chunk-596c6184")]).then(n.bind(null,"10dc"))}},{path:"/image",name:"image",component:function(){return Promise.all([n.e("chunk-3fb623ce"),n.e("chunk-cf61e458")]).then(n.bind(null,"b805"))}},{path:"/tools",name:"tools",component:function(){return Promise.all([n.e("chunk-3fb623ce"),n.e("chunk-111de227")]).then(n.bind(null,"be50"))}},{path:"/worker",name:"worker",component:function(){return Promise.all([n.e("chunk-3fb623ce"),n.e("chunk-1af68a6e")]).then(n.bind(null,"5458"))}},{path:"/system",name:"system",component:function(){return Promise.all([n.e("chunk-3fb623ce"),n.e("chunk-f69c766e")]).then(n.bind(null,"83d4"))}},{path:"/finder",name:"finder",component:function(){return Promise.all([n.e("chunk-3fb623ce"),n.e("chunk-a2b32e48")]).then(n.bind(null,"4ebf"))}},{path:"/installed",name:"installed",component:function(){return Promise.all([n.e("chunk-2d0c49e7"),n.e("chunk-93f94cf8")]).then(n.bind(null,"e6b9"))}},{path:"/account",name:"account",component:function(){return n.e("chunk-88e69aa8").then(n.bind(null,"3e1f"))}},{path:"/settings",name:"settings",component:function(){return n.e("chunk-197bc330").then(n.bind(null,"2b51"))}},{path:"/dev",name:"dev",component:function(){return n.e("chunk-46496dac").then(n.bind(null,"44e73"))}},{path:"/:catchAll(.*)",name:"finder",component:function(){return Promise.all([n.e("chunk-3fb623ce"),n.e("chunk-a2b32e48")]).then(n.bind(null,"4ebf"))}}]),ye=Object(de["a"])({history:Object(de["b"])(),routes:ve}),xe=ye,Pe=n("c7eb"),Ce=n("1da1"),Se=(n("b0c0"),n("159b"),n("b64b"),n("e9c4"),n("0eaf")),Ne=function(e,t){var n=!1;return t.some((function(t){return t.name===e.name&&(n=!0),n})),n},Te=Object(fe["a"])({state:{totalPlugins:[],localPlugins:[],searchValue:"",active:["finder"]},mutations:{commonUpdate:function(e,t){Object.keys(t).forEach((function(n){e[n]=t[n]}))},setSearchValue:function(e,t){e.searchValue=t}},actions:{init:function(e){return Object(Ce["a"])(Object(Pe["a"])().mark((function t(){var n,c,a;return Object(Pe["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n=e.commit,t.next=3,Se["a"].getTotalPlugins();case 3:c=t.sent,a=window.market.getLocalPlugins(),c.forEach((function(e){e.isdownload=Ne(e,a),e.isloading=!1})),a.forEach((function(e){e.isloading=!1})),n("commonUpdate",{localPlugins:a,totalPlugins:c});case 8:case"end":return t.stop()}}),t)})))()},startDownload:function(e,t){var n=e.commit,c=e.state,a=JSON.parse(JSON.stringify(c.totalPlugins));a.forEach((function(e){e.name===t&&(e.isloading=!0)})),n("commonUpdate",{totalPlugins:a})},startUnDownload:function(e,t){var n=e.commit,c=(e.state,window.market.getLocalPlugins());c.forEach((function(e){e.name===t&&(e.isloading=!0)})),n("commonUpdate",{localPlugins:c})},errorUnDownload:function(e,t){var n=e.commit,c=(e.state,window.market.getLocalPlugins());c.forEach((function(e){e.name===t&&(e.isloading=!1)})),n("commonUpdate",{localPlugins:c})},successDownload:function(e,t){var n=e.commit,c=e.state,a=JSON.parse(JSON.stringify(c.totalPlugins));a.forEach((function(e){e.name===t&&(e.isloading=!1,e.isdownload=!0)}));var r=window.market.getLocalPlugins();n("commonUpdate",{totalPlugins:a,localPlugins:r})},updateLocalPlugin:function(e){return Object(Ce["a"])(Object(Pe["a"])().mark((function t(){var n,c,a;return Object(Pe["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n=e.commit,c=window.market.getLocalPlugins(),t.next=4,Se["a"].getTotalPlugins();case 4:a=t.sent,a.forEach((function(e){e.isdownload=Ne(e,c),e.isloading=!1})),n("commonUpdate",{localPlugins:c,totalPlugins:a});case 7:case"end":return t.stop()}}),t)})))()}},modules:{}}),Ve=(n("375b"),n("664e"),n("47e2")),_e=n("5530"),De={"en-US":{feature:{market:{title:"Market",search:"Search Plugins",searchResult:"Search Results",explore:"Explore",efficiency:"Efficiency",searchTool:"Search Tools",imageTool:"Image Tools",developTool:"Develop Tools",systemTool:"System Tools",finder:{must:"Necessary",recommended:"Recommended",lastUpdated:"Total"},install:"Install"},installed:{title:"Installed",tips1:"There are no plug-ins.",tips2:"Go to the plugin market and choose the plugin to install!",developer:"Developer",unknown:"Unknown",remove:"Remove",functionKey:"Function Key",detailInfo:"Detail Info",addToPanel:"Click the + sign to pin the keyword to the super panel",removeFromPanel:"Click the - sign to remove the keyword from the super panel"},settings:{title:"Account And Setting",account:{accountInfo:"Account Info",tips1:"rubick",tips2:"After the software preferences are set, please restart the software. Please go to the mini program set avatar and nickname.",themeColor:"Theme Setting",spring:"Spring",summer:"Summer",autumn:"Autumn",winter:"Winter",personalized:"Personalized",greeting:"Search Box Greeting",logo:"Avatar",replace:"Repalce Logo",reset:"Reset Default",name:"User Name"},basic:{title:"Basic",shortcutKey:"Shortcut Key",showOrHiddle:"Show Or Hiddle",screenCapture:"Screen Capture",common:"Common",autoPaste:"Auto Paste",autoBoot:"Auto Boot Up",spaceExec:"Space Execution",on:"on",off:"off",theme:"Theme",darkMode:"Dark Mode",language:"Language",changeLang:"Change Language",cn:"简体中文",en:"English"},global:{title:"Global Shortcut Key",instructions:"Instructions and examples",tips:"Press the shortcut key, automatically search for the corresponding keyword, when the keyword result is exactly matched, and the result is unique, it will point directly to this function.",example:"Example",example1:"Shortcut key 「Alt + W」 keyword 「Wechat」",tips1:"Press 「Alt + W」 to open the local Wechat app directly",example2:"Shortcut key 「Ctrl + Alt + A」 keyword 「screenshot」",tips2:"Press 「Ctrl + Alt + A」 to take a screenshot",shortcutKey:"Shortcut Key",funtionKey:"Funtion Key",addShortcutKey:"ADD Global Shortcut Key",addShortcutKeyTips:"Press the function keys (Ctrl, Shift, {optionKeyName}) first, and then press other normal keys. Or press the F1-F12 button."},superPanel:{title:"Super Panel",tips:"Please select the common plug-ins that need to be added to the super panel.",add:"Add",remove:"Revome"},intranet:{title:"Intranet Deployment",tips:"If publishing plug-ins to the public network npm does not meet your company's security requirements, rubick supports private network private sources and private plug-in libraries. If you need private network deployment, you can configure the following rules.",npmMirror:"npm mirror",dbUrl:"database url",accessToken:"access token",placeholder:"required for private network gitlab warehouse"},localstart:{title:"Local Start"},database:{title:"Data Synchronization"}},dev:{title:"Developer",tips:"The rubick plug-in system relies on npm management. Local debugging needs to first execute npm link in the current directory of the local plug-in.",pluginName:"Plugin Name",install:"Install",refreshPlugins:"Refresh Plugins",installSuccess:"{pluginName} Install Successed!",refreshSuccess:"{pluginName} Refresh Successed!"}}}},Ae={"zh-CN":{feature:{market:{title:"插件市场",search:"搜索插件",searchResult:"搜索结果",explore:"探索",efficiency:"效率",searchTool:"搜索工具",imageTool:"图像",developTool:"开发者",systemTool:"系统",finder:{must:"必备",recommended:"推荐",lastUpdated:"全部"},install:"安装"},installed:{title:"已安装",tips1:"暂无任何插件",tips2:"去插件市场选择安装合适的插件吧！",developer:"开发者",unknown:"未知",remove:"移除",functionKey:"功能关键字",detailInfo:"详情介绍",addToPanel:"点击+号，固定关键词到超级面板",removeFromPanel:"点击-号，从超级面板移除关键词"},settings:{title:"账户和设置",account:{accountInfo:"账户信息",tips1:"rubick 用户",tips2:"软件偏好设置完成后需重启软件，头像和昵称请前往小程序设置",themeColor:"主题设置",spring:"立春",summer:"立夏",autumn:"立秋",winter:"立冬",personalized:"用户设置",greeting:"搜索框欢迎语",logo:"头像",replace:"替换",reset:"恢复默认设置",name:"用户名"},basic:{title:"基本设置",shortcutKey:"快捷键",showOrHiddle:"显示/隐藏快捷键",screenCapture:"截屏",common:"通用",autoPaste:"输入框自动粘贴",autoBoot:"开机启动",spaceExec:"空格执行",on:"开",off:"关",theme:"主题",darkMode:"暗黑模式",language:"语言",changeLang:"切换语言",cn:"简体中文",en:"English"},global:{title:"全局快捷键",instructions:"说明及示例",tips:"按下快捷键，自动搜索对应关键字，当关键字结果完全匹配，且结果唯一时，会直接指向该功能。",example:"示例",example1:"快捷键 「 Alt + W」 关键字 「 微信」",tips1:"按下Alt + W 直接打开本地微信应用",example2:"快捷键 「 Ctrl + Alt + A」 关键字 「 截屏」",tips2:"按下 Ctrl + Alt + A 进行截屏",shortcutKey:"快捷键",funtionKey:"功能关键字",addShortcutKey:"新增全局快捷功能",addShortcutKeyTips:"先按功能键（Ctrl、Shift、{optionKeyName}），再按其他普通键。或按 F1-F12 单键。"},superPanel:{title:"超级面板设置",tips:"请选择需要添加到超级面板中的常用插件",add:"添加",remove:"移除"},intranet:{title:"内网部署配置",tips:"把插件发布到公网 npm 如果不符合您的公司安全要求，rubick 支持内网私有源和私有插件库，如果您需要内网部署使用，可以自行配置以下规则。",npmMirror:"npm 源",dbUrl:"database url",accessToken:"access token",placeholder:"内网gitlab仓库必填"},localstart:{title:"本地启动"},database:{title:"多端数据同步"}},dev:{title:"开发者",tips:"rubick 插件系统依托于 npm 管理，本地调试需要先在本地插件当前目录执行 npm link",pluginName:"插件名称",install:"安装",refreshPlugins:"刷新插件",installSuccess:"{pluginName}安装成功！",refreshSuccess:"{pluginName}刷新成功！"}}}},Ee=Object(_e["a"])(Object(_e["a"])({},De),Ae),Ue=Ee,Ie=pe["a"].getConfig(),Ke=Ie.perf,Le=Object(Ve["a"])({legacy:!1,locale:Ke.common.lang||"zh-CN",fallbackLocale:"zh-CN",messages:Ue}),Re=Le,ze=pe["a"].getConfig();$.a.config({theme:ze.perf.custom||{}}),window.rubick.changeTheme=function(){var e=pe["a"].getConfig();$.a.config({theme:e.perf.custom||{}})},Object(W["createApp"])(we).use(Re).use(Te).use(a.a).use(o.a).use(i.a).use(l.a).use(f.a).use(b.a).use(h.a).use(k.a).use(j.a).use(v.a).use(x.a).use(C.a).use(N.a).use(V.a).use(D.a).use(E.a).use(I.a).use(L.a).use(z.a).use(F.a).use(xe).use(J["a"]).mount("#app")},f17b:function(e,t,n){"use strict";n("c67a")}});