(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-111de227"],{3271:function(e,t,n){},be50:function(e,t,n){"use strict";n.r(t);var c=n("c7eb"),o=n("1da1"),a=(n("d81d"),n("d3b7"),n("159b"),n("b0c0"),n("7a23")),r=n("0eaf"),u=n("be89"),s=n("5502"),l={class:"tools"},i={__name:"tools",setup:function(e){var t=Object(s["b"])(),n=Object(a["computed"])((function(){return t.state.totalPlugins})),i=Object(a["ref"])([]);Object(a["onBeforeMount"])(Object(o["a"])(Object(c["a"])().mark((function e(){return Object(c["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,r["a"].getSearchDetail();case 2:i.value=e.sent;case 3:case"end":return e.stop()}}),e)}))));var b=Object(a["computed"])((function(){var e=i.value||[];return e.length?e.map((function(e){var t=null;return n.value.forEach((function(n){n.name===e&&(t=n)})),t})):[]}));return function(e,t){return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",l,[Object(a["unref"])(b)&&Object(a["unref"])(b).length?(Object(a["openBlock"])(),Object(a["createBlock"])(u["a"],{key:0,onDownloadSuccess:e.downloadSuccess,title:e.$t("feature.market.searchTool"),list:Object(a["unref"])(b)},null,8,["onDownloadSuccess","title","list"])):Object(a["createCommentVNode"])("",!0)])}}};n("f1e9");const b=i;t["default"]=b},d81d:function(e,t,n){"use strict";var c=n("23e7"),o=n("b727").map,a=n("1dde"),r=a("map");c({target:"Array",proto:!0,forced:!r},{map:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}})},f1e9:function(e,t,n){"use strict";n("3271")}}]);