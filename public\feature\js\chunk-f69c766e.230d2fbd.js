(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-f69c766e"],{2957:function(e,t,n){"use strict";n("aed9e")},"83d4":function(e,t,n){"use strict";n.r(t);var c=n("c7eb"),a=n("1da1"),o=(n("d81d"),n("d3b7"),n("159b"),n("b0c0"),n("7a23")),r=n("0eaf"),u=n("be89"),s=n("5502"),i={class:"system"},l={__name:"system",setup:function(e){var t=Object(s["b"])(),n=Object(o["computed"])((function(){return t.state.totalPlugins})),l=Object(o["ref"])([]);Object(o["onBeforeMount"])(Object(a["a"])(Object(c["a"])().mark((function e(){return Object(c["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,r["a"].getSystemDetail();case 2:l.value=e.sent;case 3:case"end":return e.stop()}}),e)}))));var b=Object(o["computed"])((function(){var e=l.value||[];return e.length?e.map((function(e){var t=null;return n.value.forEach((function(n){n.name===e&&(t=n)})),t})):[]}));return function(e,t){return Object(o["openBlock"])(),Object(o["createElementBlock"])("div",i,[Object(o["unref"])(b)&&Object(o["unref"])(b).length?(Object(o["openBlock"])(),Object(o["createBlock"])(u["a"],{key:0,onDownloadSuccess:e.downloadSuccess,title:e.$t("feature.market.systemTool"),list:Object(o["unref"])(b)},null,8,["onDownloadSuccess","title","list"])):Object(o["createCommentVNode"])("",!0)])}}};n("2957");const b=l;t["default"]=b},aed9e:function(e,t,n){},d81d:function(e,t,n){"use strict";var c=n("23e7"),a=n("b727").map,o=n("1dde"),r=o("map");c({target:"Array",proto:!0,forced:!r},{map:function(e){return a(this,e,arguments.length>1?arguments[1]:void 0)}})}}]);