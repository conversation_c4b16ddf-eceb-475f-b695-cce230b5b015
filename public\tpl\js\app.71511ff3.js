(function(e){function t(t){for(var r,a,s=t[0],u=t[1],i=t[2],p=0,d=[];p<s.length;p++)a=s[p],Object.prototype.hasOwnProperty.call(o,a)&&o[a]&&d.push(o[a][0]),o[a]=0;for(r in u)Object.prototype.hasOwnProperty.call(u,r)&&(e[r]=u[r]);l&&l(t);while(d.length)d.shift()();return c.push.apply(c,i||[]),n()}function n(){for(var e,t=0;t<c.length;t++){for(var n=c[t],r=!0,s=1;s<n.length;s++){var u=n[s];0!==o[u]&&(r=!1)}r&&(c.splice(t--,1),e=a(a.s=n[0]))}return e}var r={},o={app:0},c=[];function a(t){if(r[t])return r[t].exports;var n=r[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,a),n.l=!0,n.exports}a.m=e,a.c=r,a.d=function(e,t,n){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},a.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,t){if(1&t&&(e=a(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(a.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)a.d(n,r,function(t){return e[t]}.bind(null,r));return n},a.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="";var s=window["webpackJsonp"]=window["webpackJsonp"]||[],u=s.push.bind(s);s.push=t,s=s.slice();for(var i=0;i<s.length;i++)t(s[i]);var l=u;c.push([0,"chunk-vendors"]),n()})({0:function(e,t,n){e.exports=n("cd49")},"0378":function(e,t,n){"use strict";n("c7e8")},"0861":function(e,t,n){},3809:function(e,t,n){"use strict";n("0861")},c7e8:function(e,t,n){},cd49:function(e,t,n){"use strict";n.r(t);var r=n("79c4"),o=n("5084"),c={__name:"App",setup(e){const t=Object(o["d"])();return window.rubick.onPluginEnter(({code:e,type:n,payload:r})=>{const o=window.exports[e];"none"!==o.mode?t.push({name:o.mode,params:{code:e,type:n,payload:r}}):o.args.enter&&o.args.enter({code:e,type:n,payload:r})}),(e,t)=>{const n=Object(r["w"])("router-view");return Object(r["r"])(),Object(r["d"])(n)}}};n("0378");const a=c;var s=a;const u={class:"list-container"},i={class:"options"},l=["onClick"],p=["src"],d={class:"content"},b={class:"title"},v={class:"desc"};var f=Object(r["h"])({__name:"List",setup(e){const{ipcRenderer:t}=window.require("electron"),n=Object(o["c"])(),c=60,a=10,s=60,{code:f,type:O,payload:j}=n.params,y=window.exports[f];window.rubick.setExpendHeight(s);const w=Object(r["u"])([]);Object(r["B"])([w],()=>{const e=w.value.length>a?a*c:c*w.value.length;window.rubick.setExpendHeight(s+e)}),y.args.enter&&y.args.enter({code:f,type:O,payload:j},e=>{w.value=e});const g=Object(r["u"])(0);t.on("changeCurrent",(e,t)=>{g.value+t>w.value.length-1||w.value+t<0||(g.value=g.value+t)}),window.rubick.setSubInput(({text:e})=>{y.args.search&&y.args.search({code:f,type:"",payload:[]},e,e=>{w.value=e||[]})},"搜索");const h=e=>{y.args.select&&y.args.select({code:f,type:"",payload:[]},e)},m=e=>{if("Enter"===e.code)return h(w.value[g.value]);let t=0;"ArrowDown"===e.code&&(t=1),"ArrowUp"===e.code&&(t=-1),w.value.length&&(g.value+t>w.value.length-1||g.value+t<0||(g.value=g.value+t))};return window.addEventListener("keydown",m),Object(r["o"])(()=>{window.removeEventListener("keydown",m)}),(e,t)=>(Object(r["r"])(),Object(r["f"])("div",u,[Object(r["C"])(Object(r["g"])("div",i,[(Object(r["r"])(!0),Object(r["f"])(r["a"],null,Object(r["v"])(w.value,(e,t)=>(Object(r["r"])(),Object(r["f"])("div",{key:t,class:Object(r["m"])(g.value===t?"active op-item":"op-item"),onClick:t=>h(e)},[e.icon?(Object(r["r"])(),Object(r["f"])("img",{key:0,class:"icon",src:e.icon},null,8,p)):Object(r["e"])("",!0),Object(r["g"])("div",d,[Object(r["g"])("div",b,Object(r["y"])(e.title),1),Object(r["g"])("div",v,Object(r["y"])(decodeURIComponent(e.description)),1)])],10,l))),128))],512),[[r["A"],!!(w.value||[]).length]])]))}});n("3809");const O=f;var j=O;const y={class:"home"};function w(e,t){return Object(r["r"])(),Object(r["f"])("div",y)}var g=n("41ec"),h=n.n(g);const m={},k=h()(m,[["render",w]]);var x=k;const _=[{path:"/list",name:"list",component:j},{path:"/doc",name:"doc",component:x}],P=Object(o["a"])({history:Object(o["b"])(),routes:_});var E=P,S=n("b082"),C=Object(S["a"])({state:{},mutations:{},actions:{},modules:{}});Object(r["c"])(s).use(C).use(E).mount("#app")}});