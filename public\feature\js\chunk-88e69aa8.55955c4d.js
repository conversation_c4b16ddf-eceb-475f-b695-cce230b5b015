(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-88e69aa8"],{"3e1f":function(e,t,n){"use strict";n.r(t);n("c346");var c=n("e63d"),r=n.n(c),a=n("7a23"),u=n("e1bd"),o=n("f5e0"),s={class:"account"},i=["src"],b={__name:"index",setup:function(e){var t=Object(a["ref"])(window.rubick.dbStorage.getItem("rubick-user-info")),n=Object(a["ref"])(""),c=Object(u["a"])(),b=Object(a["ref"])(!1),l=function(){b.value=!0,n.value||t.value||o["a"].getScanCode({scene:c}).then((function(e){n.value="data:image/png;base64,".concat(e.dataUrl)}))},d=null;return Object(a["watch"])([b],(function(){b.value?d=setInterval((function(){o["a"].checkLoginStatus({scene:c}).then((function(e){console.log(e),e.openId&&(window.rubick.dbStorage.setItem("rubick-user-info",e),t.value=e,r.a.success("登录成功！"),b.value=!1,clearInterval(d),d=null)}))}),2e3):(clearInterval(d),d=null)})),function(e,c){var r=Object(a["resolveComponent"])("a-button"),u=Object(a["resolveComponent"])("a-result"),o=Object(a["resolveComponent"])("a-modal");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",s,[t.value?Object(a["createCommentVNode"])("",!0):(Object(a["openBlock"])(),Object(a["createBlock"])(u,{key:0,title:"请先登录","sub-title":"用户暂未登录，无法体验更多设置"},{extra:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(r,{onClick:l,type:"primary"},{default:Object(a["withCtx"])((function(){return[Object(a["createTextVNode"])(" 使用微信小程序登录 ")]})),_:1})]})),_:1})),Object(a["createVNode"])(o,{footer:null,visible:b.value,"onUpdate:visible":c[0]||(c[0]=function(e){return b.value=e})},{default:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(u,{title:"请使用微信扫码登录!","sub-title":"使用微信扫描上面的 rubick 小程序二维码进行授权登录"},{icon:Object(a["withCtx"])((function(){return[Object(a["createElementVNode"])("img",{width:"200",src:n.value},null,8,i)]})),_:1})]})),_:1},8,["visible"])])}}},l=(n("7251"),n("6b0d")),d=n.n(l);const f=d()(b,[["__scopeId","data-v-2bc021e6"]]);t["default"]=f},7251:function(e,t,n){"use strict";n("9d3e")},"9d3e":function(e,t,n){},a479:function(e,t,n){},c346:function(e,t,n){"use strict";n("fe5b"),n("a479")},e1bd:function(e,t,n){"use strict";n.d(t,"a",(function(){return c}));let c=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce((e,t)=>(t&=63,e+=t<36?t.toString(36):t<62?(t-26).toString(36).toUpperCase():t>62?"-":"_",e),"")},f5e0:function(e,t,n){"use strict";var c=n("c7eb"),r=n("1da1"),a=n("bc3a"),u=n.n(a),o=u.a.create({baseURL:"https://rubick.vip/api/"});t["a"]={getScanCode:function(e){return Object(r["a"])(Object(c["a"])().mark((function t(){var n,r;return Object(c["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n=e.scene,t.next=3,o.get("/users/getScanCode",{params:{scene:n}});case 3:return r=t.sent,t.abrupt("return",r.data);case 5:case"end":return t.stop()}}),t)})))()},checkLoginStatus:function(e){return Object(r["a"])(Object(c["a"])().mark((function t(){var n,r;return Object(c["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n=e.scene,t.next=3,o.post("/users/checkLoginStatus",{scene:n});case 3:return r=t.sent,t.abrupt("return",r.data);case 5:case"end":return t.stop()}}),t)})))()},getUserInfo:function(e){return Object(r["a"])(Object(c["a"])().mark((function t(){var n,r;return Object(c["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n=e.openId,t.next=3,o.post("/users/getUserInfo",{openId:n});case 3:return r=t.sent,t.abrupt("return",r.data);case 5:case"end":return t.stop()}}),t)})))()}}}}]);