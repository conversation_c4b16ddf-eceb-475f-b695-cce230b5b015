.left-menu {
  width: 183px;
  // height: 100vh;
  border-right: 1px solid var(--color-border-light);
  .search-container {
    padding: 10px;
  }
  .ant-input-affix-wrapper {
    border: none;
    background: var(--color-input-hover);
    :deep(input) {
      background: none;
      color: var(--color-text-desc);
    }
    :deep(.anticon) {
      color: var(--color-text-desc);
    }
  }
  :deep(.ant-menu) {
    background: var(--color-menu-bg);
    height: 100%;
    border-right: none;
    .ant-menu-item, .ant-menu-submenu, .ant-menu-submenu-arrow {
      color: var(--color-text-content);
      &:active {
        background: none;
      }
    }
    .ant-menu-item-selected, .ant-menu-submenu-selected {
      background-color: var(--color-list-hover);
      color: var(--ant-primary-color);
      .ant-menu-submenu-arrow {
        color: var(--ant-primary-color);
      }
      &:after {
        display: none;
      }
    }
  }
}
